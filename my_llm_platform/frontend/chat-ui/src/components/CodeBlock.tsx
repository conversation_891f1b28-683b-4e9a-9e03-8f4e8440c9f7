import React, { useState } from 'react';
import SyntaxHighlighter from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/hljs';
import { useTranslation } from 'react-i18next';

interface CodeBlockProps {
  language?: string;
  children: string;
  className?: string;
}

/**
 * コードブロックコンポーネント
 * シンタックスハイライトと複製機能を提供
 */
const CodeBlock: React.FC<CodeBlockProps> = ({ language, children, className }) => {
  const { t } = useTranslation();
  const [copied, setCopied] = useState(false);

  // 複製機能
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(children);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000); // 2秒後にリセット
    } catch (err) {
      console.error('コピーに失敗しました:', err);
      // フォールバック: 古いブラウザ対応
      const textArea = document.createElement('textarea');
      textArea.value = children;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (fallbackErr) {
        console.error('フォールバックコピーも失敗しました:', fallbackErr);
      }
      document.body.removeChild(textArea);
    }
  };

  // 言語の検出
  const detectedLanguage = language || (className ? className.replace('language-', '') : 'text');

  // カスタムスタイル定義
  const customStyle = {
    margin: 0,
    borderRadius: 0,
    background: 'transparent',
    fontSize: '0.9rem',
    lineHeight: '1.5',
    fontFamily: "'Fira Code', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace",
  };

  return (
    <div className="code-block-container">
      <div className="code-block-header">
        <span className="code-language">{detectedLanguage}</span>
        <button
          className={`copy-button ${copied ? 'copied' : ''}`}
          onClick={handleCopy}
          title={copied ? t('copied') : t('copy_code')}
        >
          {copied ? (
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="20,6 9,17 4,12"></polyline>
            </svg>
          ) : (
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
          )}
        </button>
      </div>
      <div className="code-block-content">
        <SyntaxHighlighter
          language={detectedLanguage}
          style={tomorrow}
          PreTag="div"
          customStyle={customStyle}
        >
          {children}
        </SyntaxHighlighter>
      </div>
    </div>
  );
};

export default CodeBlock;
